import CrateServices
import Factory
import SwiftData
import SwiftUI

public enum UIFlags: Int {
  case showAboutView
  case showCardPreview
  case showEmptyUrlWriteAlert
  case showExtraDetailsView
}

public enum WriteViewState {
  case empty
  case fetching
  case loaded(Content)
  case error(String)
}

public struct ViewState {
  var enteredURL: String = ""
  var currentURL: String = ""
  var imageURL: URL?
  var domainImage: String?
  var lastWrittenRecord: Content?
}

@MainActor
public final class WriteViewModel: ObservableObject {
  @Published public var viewState = ViewState()
  @Published public var state: WriteViewState = .empty
  @Published public var activeFlags: Set<UIFlags> = []
  @Published public var nfcWriter = NFCWriter()
  @Published public var showExtraDetails: Bool = false
  @Published public var customCreator: String = "lilrobo"
  private var debounceWorkItem: DispatchWorkItem?

  private let unfurlService: UnfurlServiceProtocol
  private let nfcRecordService: NFCRecordServiceProtocol
  private let contentService: ContentServiceProtocol
  public var deepLinkHandler: DeepLinkHandler
  private let modelContext: ModelContext

  public init(_ deepLinkHandler: DeepLinkHandler, modelContext: ModelContext) {
    self.deepLinkHandler = deepLinkHandler
    self.modelContext = modelContext
    unfurlService = Container.shared.unfurlService.resolve()
    nfcRecordService = Container.shared.nfcRecordService.resolve()
    contentService = Container.shared.contentService.resolve()
  }

  public func fetchContentInfo() async {
    do {
      state = .fetching
      guard let url = URL(string: viewState.currentURL) else {
        state = .error("Invalid URL")
        return
      }

      // Use client-side unfurl ONLY.
      let contentData = try await unfurlService.unfurl(url: url).toModel()
      viewState.imageURL = URL(string: contentData.mediaUrl ?? "")
      state = .loaded(contentData)

      // Still send to backend, but don't use the result for UI.
      try? await contentService.unfurl(url: viewState.currentURL)

    } catch {
      print("Error: \(error)")
      state = .error(error.localizedDescription)
    }
  }

  private func handleSignedInWrite(_ record: Content) async {
    do {
      // Write NFC message
      writeNFCMessage(record)

      // Wait a brief moment to allow the server to process the write
      try await Task.sleep(nanoseconds: 500_000_000)  // 0.5 seconds

      // Clear existing content first to prevent duplication
      try contentService.deleteAllRecentContent(context: modelContext)

      let updatedContent = try await contentService.getRecent(start: 0, size: 20)
      let models = updatedContent.map { $0.toModel() }
      try contentService.saveRecentContent(models, context: modelContext)

      print("Successfully fetched \(updatedContent.count) recent content from server after write")
    } catch {
      print("Error syncing with server after write: \(error.localizedDescription)")
    }
  }

  private func handleGuestWrite(_ record: Content) throws {
    let descriptor = FetchDescriptor<RecentCollection>(
      predicate: #Predicate<RecentCollection> { collection in
        collection.name == "Recent"
      }
    )

    let recentWrites: RecentCollection
    if let existingCollection = try modelContext.fetch(descriptor).first {
      recentWrites = existingCollection
      recentWrites.updatedAt = Date()  // Update the timestamp when modifying
    } else {
      recentWrites = RecentCollection(
        name: "Recent",
        content: [],
        thumbnail: "crate_logo",
        createdAt: Date(),
        updatedAt: Date()
      )
      modelContext.insert(recentWrites)
    }

    recentWrites.content.append(record)

    if recentWrites.thumbnail != "crate_logo" {
      recentWrites.thumbnail = "crate_logo"
    }

    try modelContext.save()
    writeNFCMessage(record)
  }

  public func handleWriteButtonTapped() {
    let record = createRecordFromCurrentState()
    viewState.lastWrittenRecord = record

    do {
      try nfcRecordService.save(record)

      // Get the user's authentication state
      let isSignedIn = Container.shared.userState.resolve().isSignedIn

      if isSignedIn {
        Task {
          await handleSignedInWrite(record)
        }
      } else {
        try handleGuestWrite(record)
      }
    } catch {
      print("Error saving record or updating collection: \(error.localizedDescription)")
    }
  }

  private func createRecordFromCurrentState() -> Content {
    switch state {
    case let .loaded(existingRecord):
      return Content(
        detail: existingRecord.artist,
        title: existingRecord.name,
        mediaUrl: viewState.imageURL?.absoluteString,
        url: viewState.enteredURL,
        updatedAt: Date(),
        createdAt: Date()
      )
    case let .error(errorMessage):
      print("Creating basic record due to unfurl error: \(errorMessage)")
      return Content(
        detail: nil,
        title: nil,
        mediaUrl: nil,
        url: viewState.enteredURL,
        updatedAt: Date(),
        createdAt: Date()
      )
    case .empty, .fetching:
      return Content(
        detail: nil,
        title: nil,
        mediaUrl: nil,
        url: viewState.enteredURL,
        updatedAt: Date(),
        createdAt: Date()
      )
    }
  }

  public func handleURLChange() {
    debounceWorkItem?.cancel()

    let workItem = DispatchWorkItem { [weak self] in
      guard let self = self else { return }

      if self.viewState.currentURL.isEmpty {
        self.state = .empty
        return
      }

      Task {
        await self.fetchContentInfo()
      }
    }

    debounceWorkItem = workItem
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute: workItem)  // 300ms debounce
  }

  public func clearContentInfo() {
    viewState.enteredURL = ""
    viewState.currentURL = ""
    viewState.imageURL = nil
    viewState.domainImage = nil
    state = .empty
  }

  public func handleDeepLink(_ url: URL?) {
    if let deepLink = url?.absoluteString {
      viewState.enteredURL = deepLink
      viewState.currentURL = deepLink
      handleURLChange()
    }
  }

  public func handleInitialDeepLink() {
    if let deepLink = deepLinkHandler.deepLinkURL,
       !deepLink.absoluteString.isEmpty {
      viewState.enteredURL = deepLink.absoluteString
      viewState.currentURL = deepLink.absoluteString
      handleURLChange()
    }
  }

  public func handleRefresh() {
    if let deepLink = deepLinkHandler.deepLinkURL?.absoluteString {
      print("Current deep link on refresh: \(deepLink)")
    }
    handleURLChange()
  }

  public func writeNFCMessage(_ record: Content) {
    let messages: [(String, String)] = [
      (record.url ?? "", "U"),
      (createMetadataJSONFromRecord(record), "T")
    ]
    print("Starting NFC write with messages: \(messages)")
    nfcWriter.write(messages)
  }

  public func createMetadataJSONFromRecord(_ record: Content) -> String {
    var metadata: [String: String] = [:]
    metadata["created"] = ISO8601DateFormatter().string(from: Date())

    if showExtraDetails {
      if let detail = record.detail, !detail.isEmpty {
        metadata["artist"] = detail
      }
      if let title = record.title, !title.isEmpty {
        metadata["song"] = title
      }
      if !customCreator.isEmpty {
        metadata["creator"] = customCreator
      }
      if let mediaUrl = record.mediaUrl, !mediaUrl.isEmpty {
        metadata["imgUrl"] = mediaUrl
      }
      if let url = record.url, let domain = URL(string: url)?.host, !domain.isEmpty {
        metadata["domain"] = domain
      }
    }

    guard let jsonData = try? JSONSerialization.data(withJSONObject: metadata),
          let jsonString = String(data: jsonData, encoding: .utf8)
    else {
      print("Error creating metadata JSON")
      return "{}"
    }

    print("Writing metadata: \(jsonString)")
    return jsonString
  }
}
