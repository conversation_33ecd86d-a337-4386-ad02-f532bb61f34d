import CrateServices
import Factory
import SwiftData
import SwiftUI

struct WriteView: View {
  @Binding private var selectedTab: NavView.Tab
  @StateObject private var viewModel: WriteViewModel
  @FocusState private var isFocused: Bool
  @Environment(\.modelContext) private var modelContext

  public init(selectedTab: Binding<NavView.Tab>, deepLinkHandler: DeepLinkHandler) {
    _selectedTab = selectedTab
    _viewModel = StateObject(
      wrappedValue: WriteViewModel(
        deepLinkHandler, modelContext: Container.shared.modelContext.resolve()))
  }

  private func applyEventHandlers(_ content: some View) -> some View {
    content
      .onAppear()
      .onTapGesture {
        isFocused = false
      }
      .onReceive(viewModel.deepLinkHandler.$deepLinkURL) { url in
        viewModel.handleDeepLink(url)
      }
      .onChange(of: viewModel.viewState.enteredURL) { _, newValue in
        viewModel.viewState.currentURL = newValue
        viewModel.handleURLChange()
      }
      .sheet(
        isPresented: Binding(
          get: { viewModel.activeFlags.contains(.showExtraDetailsView) },
          set: { if !$0 { viewModel.activeFlags.remove(.showExtraDetailsView) } }
        )
      ) {
        extraDetailsSheet
      }
      .alert(
        "Empty URL",
        isPresented: Binding(
          get: { viewModel.activeFlags.contains(.showEmptyUrlWriteAlert) },
          set: { if !$0 { viewModel.activeFlags.remove(.showEmptyUrlWriteAlert) } }
        )
      ) {
        Button("OK", role: .cancel) {}
      } message: {
        Text("Please enter a valid URL before writing to NFC.")
      }
  }

  var body: some View {
    CrateViewComponent {
      applyEventHandlers(
        ScrollView {
          getMainContent()
        }
        .scrollIndicators(.hidden)
      )
    }
  }

  @ViewBuilder
  private func getMainContent() -> some View {
    ScrollView {
      CrateHeadingComponent(title: "Preview")
      getPreviewSection()
      getUrlInputSection()
      getButtonSection()
    }
    .background(
      Color.clear
        .contentShape(Rectangle())
        .onTapGesture {
          isFocused = false
        }
    )
  }

  private func getPreviewSection() -> some View {
    VStack(alignment: .leading) {
      switch viewModel.state {
      case .empty:
        CardComponent(title: "", content: "")
      case .fetching:
        VStack {
          Spacer()
          ProgressView()
          Spacer()
        }
        .frame(height: UIScreen.main.bounds.height * 0.35 + 60)
      case let .loaded(record):
        CardComponent(
          title: record.artist ?? "",
          content: record.name ?? "",
          imageURL: viewModel.viewState.imageURL
        )
      case .error:
        VStack {
          CardComponent(title: "", content: "")
        }
        .frame(height: UIScreen.main.bounds.height * 0.35 + 60)
        .onAppear {
          print("Failed to load")
        }
      }
    }
  }

  private func getUrlInputSection() -> some View {
    VStack(alignment: .center) {
      getPasteButton()
      CrateFormComponent(
        text: $viewModel.viewState.enteredURL,
        placeholder: "Enter URL here...",
        onClearClicked: viewModel.clearContentInfo
      )
      .focused($isFocused)
    }
  }

  private func getPasteButton() -> some View {
    Button(
      action: {
        if let clipboardString = UIPasteboard.general.string {
          viewModel.clearContentInfo()
          viewModel.viewState.enteredURL = clipboardString
        }
      },
      label: {
        HStack {
          Image(systemName: "doc.on.clipboard").imageScale(.small)
          Text("Paste from Clipboard").font(.subheadline)
        }
        .foregroundColor(.primary)
        .padding(.vertical, 8)
      })
  }

  private func getButtonSection() -> some View {
    HStack(spacing: 8) {
      CrateButtonComponent(
        title: "Write to NFC",
        image: "platter.filled.top.and.arrow.up.iphone",
        action: {
          switch viewModel.state {
          case .loaded, .error, .fetching:
            viewModel.handleWriteButtonTapped()
          case .empty:
            viewModel.activeFlags.insert(.showEmptyUrlWriteAlert)
          }
          isFocused = false
        },
        textColor: Color.primary,
        outlineColor: Color.primary
      )

      CrateButtonComponent(
        title: "",
        image: "text.badge.plus",
        action: {
          switch viewModel.state {
          case .loaded, .error:
            viewModel.activeFlags.insert(.showExtraDetailsView)
          default:
            break  // or handle other states as needed
          }
        },
        textColor: Color.primary,
        outlineColor: Color.primary
      )
      .frame(width: UIScreen.main.bounds.width / 8)
    }
    .frame(width: 300, height: 50)
  }

  private var extraDetailsSheet: some View {
    switch viewModel.state {
    case let .loaded(record):
      ExtraDetailsView(
        artist: record.artist ?? "",
        song: record.name ?? "",
        created: ISO8601DateFormatter().string(from: Date()),
        url: viewModel.viewState.enteredURL,
        onDetailsChanged: { showExtraDetails, creator in
          viewModel.showExtraDetails = showExtraDetails
          viewModel.customCreator = creator
        }
      )
    case .empty, .fetching, .error:
      ExtraDetailsView(
        artist: "",
        song: "",
        created: ISO8601DateFormatter().string(from: Date()),
        url: viewModel.viewState.enteredURL,
        onDetailsChanged: { showExtraDetails, creator in
          viewModel.showExtraDetails = showExtraDetails
          viewModel.customCreator = creator
        }
      )
    }
  }
}

struct CardComponent: View {
  var title: String
  var content: String
  var imageURL: URL?
  let imageHeight = UIScreen.main.bounds.height * 0.35

  var body: some View {
    VStack(alignment: .leading) {
      ZStack {
        UnfurledImage(imageURL: imageURL, height: imageHeight)
          .frame(maxWidth: .infinity)
          .frame(height: imageHeight)
      }

      VStack(alignment: .leading, spacing: 4) {
        Text(title)
          .font(.system(size: 20, weight: .bold))
          .lineLimit(1)
          .frame(maxWidth: .infinity, alignment: .leading)

        Text(content)
          .font(.system(size: 16))
          .foregroundColor(.secondary)
          .lineLimit(1)
          .frame(maxWidth: .infinity, alignment: .leading)
      }
      .padding(.horizontal, 32)
    }
  }
}

struct UnfurledImage: View {
  let imageURL: URL?
  let height: CGFloat

  var body: some View {
    ZStack {
      if let imageURL = imageURL {
        AsyncImage(url: imageURL) { phase in
          switch phase {
          case .empty:
            ProgressView()
          case let .success(image):
            image
              .resizable()
              .scaledToFit()
          case .failure:
            Image(systemName: "photo.fill")
              .resizable()
              .scaledToFit()
          @unknown default:
            ProgressView()
          }
        }
      } else {
        Image("crate_logo")
          .resizable()
          .scaledToFit()
      }
    }
    .padding(.horizontal, 8)
    .padding(.bottom, 16)
  }
}

struct WriteView_Previews: PreviewProvider {
  static var previews: some View {
    Group {
      // https://open.spotify.com/track/1epOX8KzbAWZoiS0OCE2Z2
      WriteView(
        selectedTab: .constant(.write),
        deepLinkHandler: DeepLinkHandler()
      )
      .previewDevice("iPhone SE (3rd generation)")
      .environmentObject(DeepLinkHandler())
      WriteView(
        selectedTab: .constant(.write),
        deepLinkHandler: DeepLinkHandler()
      )
      .previewDevice("iPhone 15 Pro")
      .environmentObject(DeepLinkHandler())

      WriteView(
        selectedTab: .constant(.write),
        deepLinkHandler: DeepLinkHandler()
      )
      .previewDevice("iPhone 15 Pro Max")
      .environmentObject(DeepLinkHandler())
    }
  }
}
