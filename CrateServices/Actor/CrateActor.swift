import Foundation
import SwiftData

/// Errors that can occur during Crate operations
public enum CrateError: Error {
  case collectionNotFound
  case trackNotFound
  case invalidData
  case saveFailed
}

/// Actor responsible for managing Crate data operations.
/// Handles CRUD operations for tracks, collections, and users, as well as managing relationships between them.
@available(iOS 17, *)
@ModelActor
public actor CrateActor {
  // No service dependencies; only thread-safe SwiftData access

  public func fetchCollectionDTO(id: Int) async throws -> CollectionDTO? {
    let predicate = #Predicate<Collection> { $0.serverId == id }
    guard let model = try modelContext.fetch(FetchDescriptor(predicate: predicate)).first else {
      return nil
    }
    return model.toDTO()  // Assuming Collection has toDTO() that includes tracks DTOs
  }

  public func fetchCollections() async throws -> [CollectionDTO] {
    let fetchDescriptor = FetchDescriptor<Collection>(sortBy: [SortDescriptor(\Collection.name)])
    let collections = try self.modelContext.fetch(fetchDescriptor)
    return collections.map { collection in
      CollectionDTO(
        serverId: collection.serverId,
        name: collection.name,
        thumbnail: collection.thumbnail,
        tracks: collection.tracks.map { track in
          TrackDTO(
            serverId: track.serverId,
            name: track.name,
            artist: track.artist,
            imgUrl: track.imgUrl,
            url: track.url,
            domain: track.domain,
            recentCollections: nil,
            collections: nil,
            updatedAt: track.updatedAt,
            createdAt: track.createdAt
          )
        },
        createdAt: collection.createdAt,
        updatedAt: collection.updatedAt
      )
    }
  }

  // Example: Add a collection
  public func addCollection(
    serverId: Int?,
    name: String?,
    thumbnail: String?,
    tracks: [Track] = []
  ) async throws {
    let collection = Collection(
      serverId: serverId,
      name: name,
      tracks: tracks,
      thumbnail: thumbnail,
      createdAt: Date(),
      updatedAt: Date()
    )
    self.modelContext.insert(collection)
    try self.modelContext.save()
  }

  // Example: Delete a collection by serverId
  public func deleteCollection(serverId: Int) async throws {
    let predicate = #Predicate<Collection> { collection in
      collection.serverId == serverId
    }
    let fetchDescriptor = FetchDescriptor<Collection>(predicate: predicate)
    let collections = try self.modelContext.fetch(fetchDescriptor)
    for collection in collections {
      self.modelContext.delete(collection)
    }
    try self.modelContext.save()
  }

  // Synchronize local collections with an array of CollectionDTOs
  public func updateCollectionsFromDTOs(_ dtos: [CollectionDTO]) async throws {

    // TEMPORARILY CALL THE INSPECTION METHOD HERE FOR DEBUGGING:
    try await inspectDuplicateTrackServerIds()
    // REMOVE OR COMMENT OUT THE ABOVE LINE AFTER DEBUGGING
    let allTracks = try self.modelContext.fetch(FetchDescriptor<Track>())
    var trackByServerId = [Int: Track](
      uniqueKeysWithValues: allTracks.compactMap { track in
        guard let id = track.serverId else { return nil }
        return (id, track)
      }
    )

    let existingCollections = try self.modelContext.fetch(FetchDescriptor<Collection>())
    var collectionByServerId = [Int: Collection](
      uniqueKeysWithValues: existingCollections.compactMap { collection in
        guard let id = collection.serverId else { return nil }
        return (id, collection)
      }
    )

    let dtoServerIds = Set(dtos.compactMap { $0.serverId })
    removeCollectionsNotInDTOs(existingCollections, dtoServerIds: dtoServerIds)
    updateOrCreateCollections(
      dtos, collectionByServerId: &collectionByServerId, trackByServerId: &trackByServerId)
    try self.modelContext.save()
  }

  private func removeCollectionsNotInDTOs(
    _ existingCollections: [Collection], dtoServerIds: Set<Int>
  ) {
    for collection in existingCollections {
      if let id = collection.serverId, !dtoServerIds.contains(id) {
        self.modelContext.delete(collection)
      }
    }
  }

  private func updateOrCreateCollections(
    _ dtos: [CollectionDTO], collectionByServerId: inout [Int: Collection],
    trackByServerId: inout [Int: Track]
  ) {
    for dto in dtos {
      guard let serverId = dto.serverId else { continue }
      if let existing = collectionByServerId[serverId] {
        updateCollection(existing, with: dto, trackByServerId: &trackByServerId)
      } else {
        let newCollection = Collection(
          serverId: serverId,
          name: dto.name,
          tracks: [],
          thumbnail: dto.thumbnail,
          createdAt: dto.createdAt,
          updatedAt: dto.updatedAt
        )
        if let dtoTracks = dto.tracks {
          for dtoTrack in dtoTracks {
            if let trackId = dtoTrack.serverId,
               let track = trackByServerId[trackId] {
              newCollection.tracks.append(track)
            }
          }
        }
        self.modelContext.insert(newCollection)
        collectionByServerId[serverId] = newCollection
      }
    }
  }

  private func updateCollection(
    _ collection: Collection, with dto: CollectionDTO, trackByServerId: inout [Int: Track]
  ) {
    collection.name = dto.name
    collection.thumbnail = dto.thumbnail
    collection.updatedAt = dto.updatedAt
    collection.createdAt = dto.createdAt
    if let dtoTracks = dto.tracks {
      let dtoTrackIds = dtoTracks.compactMap { $0.serverId }
      collection.tracks.removeAll { track in
        guard let trackId = track.serverId else { return true }
        return !dtoTrackIds.contains(trackId)
      }
      for dtoTrack in dtoTracks {
        if let trackId = dtoTrack.serverId,
           let track = trackByServerId[trackId],
           !collection.tracks.contains(where: { $0 === track }) {
          collection.tracks.append(track)
        }
      }
    }
  }

  // MARK: Track in Collection Managemement

  // Function to check for tracks with duplicate serverIds
  public func inspectDuplicateTrackServerIds() async throws {
    print("🔍 Starting inspection for duplicate Track serverIds...")
    let context = self.modelContext  // Use the actor's model context

    let allTracksFetchDescriptor = FetchDescriptor<Track>(sortBy: [SortDescriptor(\Track.serverId)])
    let allTracks: [Track]
    do {
      allTracks = try context.fetch(allTracksFetchDescriptor)
    } catch {
      print("❌ Error fetching tracks: \(error.localizedDescription)")
      throw error
    }

    if allTracks.isEmpty {
      print("ℹ️ No tracks found in the database.")
      return
    }

    print("ℹ️ Total tracks fetched: \(allTracks.count)")

    // Group tracks by serverId
    // We can't use Dictionary(grouping:by:) directly if serverId is optional
    // and we want to handle nil serverIds separately or ignore them for duplication check.

    var tracksGroupedByServerId = [Int: [Track]]()
    var tracksWithNilServerIdCount = 0

    for track in allTracks {
      if let serverId = track.serverId {
        tracksGroupedByServerId[serverId, default: []].append(track)
      } else {
        tracksWithNilServerIdCount += 1
      }
    }

    if tracksWithNilServerIdCount > 0 {
      print("ℹ️ Found \(tracksWithNilServerIdCount) track(s) with a nil serverId.")
    }

    var foundDuplicates = false
    // Iterate ONLY over the groups that actually have duplicates
    for (serverId, tracksInGroup) in tracksGroupedByServerId where tracksInGroup.count > 1 {
      foundDuplicates = true  // Now, if we enter this loop, we definitely have duplicates
      print("\n🚨 DUPLICATE serverId FOUND: \(serverId) (Count: \(tracksInGroup.count))")
      for (index, track) in tracksInGroup.enumerated() {
        print("  --- Duplicate Instance #\(index + 1) ---")
        print("    Track Name: \(track.name ?? "N/A")")
        print("    Artist: \(track.artist ?? "N/A")")
        print("    URL: \(track.url ?? "N/A")")
        print("    Image URL: \(track.imgUrl ?? "N/A")")
        print("    Created At: \(track.createdAt?.description ?? "N/A")")
        print("    Updated At: \(track.updatedAt?.description ?? "N/A")")
        // print("    Persistent ID: \(track.persistentModelID.storeIdentifier ?? "N/A")")
      }
    }

    if !foundDuplicates {
      print("✅ No duplicate Track serverIds found (excluding tracks with nil serverId).")
    }
    print("🔍 Inspection finished.")
  }

  // Helper function to fetch or create a Track model
  private func fetchOrCreateTrack(from dto: TrackDTO) throws -> Track {
    if let serverId = dto.serverId {
      // Try to fetch existing track by serverId
      let predicate = #Predicate<Track> { $0.serverId == serverId }
      let descriptor = FetchDescriptor<Track>(predicate: predicate)
      if let existingTrack = try modelContext.fetch(descriptor).first {
        // Optionally update existing track's properties from DTO if needed
        // existingTrack.name = dto.name ?? existingTrack.name
        // ... etc. ...
        // existingTrack.updatedAt = dto.updatedAt ?? existingTrack.updatedAt ?? Date()
        return existingTrack
      }
    }
    // If not found by serverId, or if serverId is nil, create a new one
    let newTrack = dto.toModel()  // Uses your TrackDTO.toModel()
    // If dto.serverId was nil, newTrack.serverId will be nil.
    // If dto.serverId was provided but track not found, newTrack.serverId will have that value.
    modelContext.insert(newTrack)
    return newTrack
  }

  public func addTrack(_ trackDTO: TrackDTO, toCollectionWithServerId collectionServerId: Int)
  async throws {
    // 1. Fetch the Collection model
    let collectionPredicate = #Predicate<Collection> { $0.serverId == collectionServerId }
    guard
      let collectionModel = try modelContext.fetch(FetchDescriptor(predicate: collectionPredicate))
        .first
    else {
      throw CrateError.collectionNotFound
    }

    // 2. Fetch or Create the Track model
    var trackModel: Track

    if let trackIdForLookup = trackDTO.serverId {  // Use serverId from DTO for lookup
      let trackPredicate = #Predicate<Track> { $0.serverId == trackIdForLookup }
      if let existingTrack = try modelContext.fetch(FetchDescriptor(predicate: trackPredicate))
          .first {
        trackModel = existingTrack
        // Optionally, update existing track's details from trackDTO if needed
        // trackModel.name = trackDTO.name ?? trackModel.name
        // ... etc.
        // trackModel.updatedAt = Date()
      } else {
        // Track with this serverId not found, so create it
        trackModel = trackDTO.toModel()  // serverId is set by toModel()
        // It's crucial that trackDTO.serverId was indeed the ID we intended if it wasn't found.
        modelContext.insert(trackModel)
      }
    } else {
      // trackDTO.serverId is nil, implies creating a new track that doesn't have a serverId yet.
      // This scenario needs careful handling for how serverId is assigned later if synced with a backend.
      // For now, assume we create it locally.
      trackModel = trackDTO.toModel()
      // A local-only track might not have a serverId initially, or you assign a temporary one.
      modelContext.insert(trackModel)
      // If your app always expects a serverId, then a DTO with nil serverId might be an error here,
      // or you have a flow to get a serverId from a backend API after creation.
    }

    // 3. Add track to collection's tracks (if not already there)
    if !collectionModel.tracks.contains(where: {
      $0.persistentModelID == trackModel.persistentModelID
    }) {  // Compare by model ID for uniqueness in the array
      // Or if serverId is reliable: { $0.serverId == trackModel.serverId && trackModel.serverId != nil }
      collectionModel.tracks.append(trackModel)
      collectionModel.updatedAt = Date()
    }

    // 4. Save changes
    try modelContext.save()
  }

  public func addContent(_ contentDTO: ContentDTO, toCollectionWithServerId collectionServerId: Int)
  async throws {
    // 1. Fetch the Collection model
    let collectionPredicate = #Predicate<Collection> { $0.serverId == collectionServerId }
    guard
      let collectionModel = try modelContext.fetch(FetchDescriptor(predicate: collectionPredicate))
        .first
    else {
      throw CrateError.collectionNotFound
    }

    // 2. Convert ContentDTO to Track model for local storage compatibility
    // Since our local storage still uses Track models, we convert content to track
    let trackModel: Track

    if let contentServerId = contentDTO.serverId {
      // Check if we already have a track with this serverId
      let trackPredicate = #Predicate<Track> { $0.serverId == contentServerId }
      if let existingTrack = try modelContext.fetch(FetchDescriptor(predicate: trackPredicate))
          .first {
        trackModel = existingTrack
        // Optionally update existing track's details from contentDTO
        trackModel.name = contentDTO.title ?? trackModel.name
        trackModel.artist = contentDTO.detail ?? trackModel.artist
        trackModel.imgUrl = contentDTO.mediaUrl ?? trackModel.imgUrl
        trackModel.url = contentDTO.url ?? trackModel.url
        trackModel.updatedAt = Date()
      } else {
        // Create new track from content
        trackModel = Track(
          serverId: contentServerId,
          name: contentDTO.title,
          artist: contentDTO.detail,
          imgUrl: contentDTO.mediaUrl,
          url: contentDTO.url,
          domain: URL(string: contentDTO.url ?? "")?.host,
          updatedAt: contentDTO.updatedAt ?? Date(),
          createdAt: contentDTO.createdAt ?? Date()
        )
        modelContext.insert(trackModel)
      }
    } else {
      // ContentDTO has no serverId, create local track
      trackModel = Track(
        serverId: nil,
        name: contentDTO.title,
        artist: contentDTO.detail,
        imgUrl: contentDTO.mediaUrl,
        url: contentDTO.url,
        domain: URL(string: contentDTO.url ?? "")?.host,
        updatedAt: contentDTO.updatedAt ?? Date(),
        createdAt: contentDTO.createdAt ?? Date()
      )
      modelContext.insert(trackModel)
    }

    // 3. Add track to collection's tracks (if not already there)
    if !collectionModel.tracks.contains(where: {
      $0.persistentModelID == trackModel.persistentModelID
    }) {
      collectionModel.tracks.append(trackModel)
      collectionModel.updatedAt = Date()
    }

    // 4. Save changes
    try modelContext.save()
  }

  public func removeTrack(trackServerId: Int, fromCollectionWithServerId collectionServerId: Int)
  async throws {
    // 1. Fetch the Collection model
    let collectionPredicate = #Predicate<Collection> { $0.serverId == collectionServerId }
    guard
      let collectionModel = try modelContext.fetch(FetchDescriptor(predicate: collectionPredicate))
        .first
    else {
      throw CrateError.collectionNotFound  // Or your specific error
    }

    // 2. Remove the track from the collection's tracks relationship
    //    It's important to remove based on a stable identifier.
    //    If serverId is reliable and unique for tracks within the DB:
    let initialTrackCount = collectionModel.tracks.count
    collectionModel.tracks.removeAll { $0.serverId == trackServerId }

    if collectionModel.tracks.count < initialTrackCount {
      collectionModel.updatedAt = Date()  // Update timestamp if a track was actually removed
      print("Track with serverId \(trackServerId) removed from collection \(collectionServerId).")
    } else {
      print(
        "Track with serverId \(trackServerId) not found in collection \(collectionServerId). No changes made."
      )
      // Optionally, you could throw an error here if the track was expected to be present.
      // throw CrateError.trackNotFoundInCollection
    }

    // 3. Save changes
    //    Note: Deleting a track from a collection's 'tracks' array only breaks the relationship.
    //    It does NOT delete the Track object itself from the database.
    //    If you wanted to delete the Track object if it's no longer in any collections,
    //    that would be more complex "orphan" cleanup logic.
    try modelContext.save()
  }

  // MARK: - Recent Collection Management

  public func fetchRecentCollection() async throws -> RecentCollectionDTO? {
    let descriptor = FetchDescriptor<RecentCollection>(
      predicate: #Predicate<RecentCollection> { collection in
        collection.name == "Recent"
      }
    )
    let collections = try self.modelContext.fetch(descriptor)

    guard let recentCollection = collections.first else {
      return nil
    }

    return RecentCollectionDTO(
      name: recentCollection.name,
      thumbnail: recentCollection.thumbnail,
      contents: recentCollection.content.map { content in
        ContentDTO(
          serverId: content.serverId,
          title: content.title,
          detail: content.detail,
          mediaUrl: content.mediaUrl,
          url: content.url,
          updatedAt: content.updatedAt,
          createdAt: content.createdAt
        )
      },
      createdAt: recentCollection.createdAt,
      updatedAt: recentCollection.updatedAt
    )
  }

  public func addRecentCollection(name: String, thumbnail: String, tracks dtos: [TrackDTO])
  async throws {
    var trackModels: [Track] = []
    for dto in dtos {
      trackModels.append(try fetchOrCreateTrack(from: dto))
    }

    let recentCollection = RecentCollection(
      name: name,
      tracks: trackModels,
      thumbnail: thumbnail,
      createdAt: Date(),
      updatedAt: Date()
    )
    self.modelContext.insert(recentCollection)
    try self.modelContext.save()
  }

  public func updateRecentCollection(tracks dtos: [TrackDTO]) async throws {
    let descriptor = FetchDescriptor<RecentCollection>(
      predicate: #Predicate<RecentCollection> { $0.name == "Recent" }
    )
    guard let recentCollection = try modelContext.fetch(descriptor).first else {
      // Optionally, create the "Recent" collection if it doesn't exist
      // try await addRecentCollection(name: "Recent", thumbnail: "", tracks: dtos)
      // return
      throw CrateError.collectionNotFound
    }

    var trackModels: [Track] = []
    for dto in dtos {
      trackModels.append(try fetchOrCreateTrack(from: dto))
    }

    recentCollection.tracks.removeAll()  // Clear existing relationships
    recentCollection.tracks.append(contentsOf: trackModels)  // Add resolved (existing or new) tracks
    recentCollection.updatedAt = Date()

    try self.modelContext.save()
  }

  public func removeTrackFromRecent(_ track: TrackDTO) async throws {
    let descriptor = FetchDescriptor<RecentCollection>(
      predicate: #Predicate<RecentCollection> { collection in
        collection.name == "Recent"
      }
    )
    let collections = try self.modelContext.fetch(descriptor)

    guard let recentCollection = collections.first else {
      throw CrateError.collectionNotFound
    }

    // Remove the track from the collection
    recentCollection.tracks.removeAll { $0.serverId == track.serverId }
    recentCollection.updatedAt = Date()

    try self.modelContext.save()
  }

  public func clearRecentCollection() async throws {
    let descriptor = FetchDescriptor<RecentCollection>(
      predicate: #Predicate<RecentCollection> { collection in
        collection.name == "Recent"
      }
    )
    let collections = try self.modelContext.fetch(descriptor)

    // Clear all tracks from each collection
    for collection in collections {
      collection.tracks.removeAll()
      collection.updatedAt = Date()
    }

    try self.modelContext.save()
  }

  // Add other CRUD methods as needed, always using self.modelContext
}
